/**
 * API Client - Optimized Axios instance with caching, retry logic, and deduplication
 */
import axios from 'axios';
import env from '../config/environment.js';

class ApiClient {
  constructor() {
    this.axiosInstance = null;
    this.isInitialized = false;
    this.requestCache = new Map();
    this.pendingRequests = new Map();
    this.retryConfig = {
      retries: 3,
      retryDelay: (retryCount) => Math.pow(2, retryCount) * 1000, // Exponential backoff
      retryCondition: (error) => {
        return axios.isAxiosError(error) &&
               (!error.response || error.response.status >= 500 || error.response.status === 429);
      }
    };
  }

  /**
   * Initialize the API client with environment configuration
   */
  async initialize() {
    if (this.isInitialized) {
      return this.axiosInstance;
    }

    const config = await env;

    // Create axios instance with optimized configuration
    this.axiosInstance = axios.create({
      baseURL: config.api.baseUrl,
      timeout: 15000, // Increased timeout for better reliability
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // Enable compression
      decompress: true,
      // Connection pooling
      maxRedirects: 3,
      // Validate status codes
      validateStatus: (status) => status < 500,
    });

    this.setupInterceptors();
    this.setupRetryLogic();
    this.isInitialized = true;

    return this.axiosInstance;
  }

  /**
   * Setup request and response interceptors with caching and deduplication
   */
  setupInterceptors() {
    // Request interceptor for caching, deduplication, and token injection
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add request timestamp for debugging
        config.metadata = { startTime: Date.now() };

        // Generate cache key for GET requests
        if (config.method === 'get') {
          const cacheKey = this.generateCacheKey(config);
          config.cacheKey = cacheKey;

          // Check cache first
          if (this.requestCache.has(cacheKey)) {
            const cached = this.requestCache.get(cacheKey);
            if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
              return Promise.reject({
                __CACHED_RESPONSE__: true,
                data: cached.data
              });
            }
          }

          // Check for pending duplicate requests
          if (this.pendingRequests.has(cacheKey)) {
            return Promise.reject({
              __DUPLICATE_REQUEST__: true,
              promise: this.pendingRequests.get(cacheKey)
            });
          }
        }

        if (import.meta.env.DEV) {
          console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for caching and error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const { config } = response;

        // Log response time in development
        if (import.meta.env.DEV && config.metadata) {
          const duration = Date.now() - config.metadata.startTime;
          console.log(`✅ ${config.method?.toUpperCase()} ${config.url} (${duration}ms)`);
        }

        // Cache GET responses
        if (config.method === 'get' && config.cacheKey) {
          this.requestCache.set(config.cacheKey, {
            data: response.data,
            timestamp: Date.now()
          });
          this.pendingRequests.delete(config.cacheKey);
        }

        return response;
      },
      (error) => {
        // Handle cached responses
        if (error.__CACHED_RESPONSE__) {
          return Promise.resolve({ data: error.data });
        }

        // Handle duplicate requests
        if (error.__DUPLICATE_REQUEST__) {
          return error.promise;
        }

        // Clean up pending requests on error
        if (error.config?.cacheKey) {
          this.pendingRequests.delete(error.config.cacheKey);
        }

        return this.handleResponseError(error);
      }
    );
  }

  /**
   * Handle response errors with detailed error processing
   */
  handleResponseError(error) {
    console.error('API request failed:', error);

    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message ||
                          error.response.data?.error ||
                          error.response.statusText ||
                          `HTTP error! status: ${error.response.status}`;

      // Handle specific status codes
      switch (error.response.status) {
        case 401:
          this.handleUnauthorized();
          break;
        case 403:
          console.warn('Access forbidden');
          break;
        case 429:
          console.warn('Rate limit exceeded');
          break;
        case 500:
          console.error('Server error');
          break;
      }

      throw new Error(errorMessage);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error: No response received from server');
    } else {
      // Something else happened
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }

  /**
   * Handle unauthorized responses
   */
  handleUnauthorized() {
    // Clear stored auth token
    this.setAuthToken(null);

    // You can add additional logic here like redirecting to login
    console.warn('Authentication required - token may be expired');
  }

  /**
   * Setup retry logic with exponential backoff
   */
  setupRetryLogic() {
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const config = error.config;

        if (!config || !this.retryConfig.retryCondition(error)) {
          return Promise.reject(error);
        }

        config.__retryCount = config.__retryCount || 0;

        if (config.__retryCount >= this.retryConfig.retries) {
          return Promise.reject(error);
        }

        config.__retryCount += 1;

        const delay = this.retryConfig.retryDelay(config.__retryCount);

        if (import.meta.env.DEV) {
          console.log(`🔄 Retrying request (${config.__retryCount}/${this.retryConfig.retries}) after ${delay}ms`);
        }

        await new Promise(resolve => setTimeout(resolve, delay));

        return this.axiosInstance(config);
      }
    );
  }

  /**
   * Generate cache key for requests
   */
  generateCacheKey(config) {
    const { method, url, params, data } = config;
    return `${method}:${url}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`;
  }

  /**
   * Clear request cache
   */
  clearCache() {
    this.requestCache.clear();
    this.pendingRequests.clear();
  }

  /**
   * GET request with optimized caching
   */
  async get(endpoint, config = {}) {
    const instance = await this.getInstance();
    const cacheKey = this.generateCacheKey({ method: 'get', url: endpoint, ...config });

    // Track pending request
    if (!this.pendingRequests.has(cacheKey)) {
      const promise = instance.get(endpoint, config);
      this.pendingRequests.set(cacheKey, promise);

      try {
        const response = await promise;
        return response.data;
      } finally {
        this.pendingRequests.delete(cacheKey);
      }
    } else {
      // Wait for existing request
      const response = await this.pendingRequests.get(cacheKey);
      return response.data;
    }
  }

  /**
   * POST request
   */
  async post(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.post(endpoint, data, config);
    return response.data;
  }

  /**
   * PUT request
   */
  async put(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.put(endpoint, data, config);
    return response.data;
  }

  /**
   * PATCH request
   */
  async patch(endpoint, data = null, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.patch(endpoint, data, config);
    return response.data;
  }

  /**
   * DELETE request
   */
  async delete(endpoint, config = {}) {
    const instance = await this.getInstance();
    const response = await instance.delete(endpoint, config);
    return response.data;
  }

  /**
   * Set authorization header
   */
  async setAuthToken(token) {
    const instance = await this.getInstance();
    if (token) {
      instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete instance.defaults.headers.common['Authorization'];
    }
  }

  /**
   * Set custom header
   */
  async setHeader(key, value) {
    const instance = await this.getInstance();
    instance.defaults.headers.common[key] = value;
  }

  /**
   * Remove custom header
   */
  async removeHeader(key) {
    const instance = await this.getInstance();
    delete instance.defaults.headers.common[key];
  }

  /**
   * Get axios instance, initializing if necessary
   */
  async getInstance() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.axiosInstance;
  }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;
