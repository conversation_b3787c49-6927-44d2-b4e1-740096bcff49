/**
 * Forgot Password Page - Email input for password reset
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Mail, AlertCircle } from 'lucide-react';
import { <PERSON>ading<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>tch<PERSON> } from '../../components/ui';
import { useAuthService } from '../../hooks';
import '../../styles/AuthPages.css';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const [captchaToken, setCaptchaToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const authService = useAuthService();

  // Clear errors when email changes
  useEffect(() => {
    if (error) setError('');
    if (captchaError) setCaptchaError('');
  }, [email]);

  const handleCaptchaChange = (token) => {
    setCaptchaToken(token);
    if (captchaError) setCaptchaError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setCaptchaError('');

    let valid = true;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!authService) {
      setError('Service not available. Please refresh the page.');
      valid = false;
    }

    if (!valid) return;

    setIsLoading(true);

    try {
      // Call the forgot password email verification API
      const result = await authService.sendForgotPasswordVerification(email, captchaToken);

      if (result.success) {
        // Navigate to OTP verification with forgot password context
        navigate('/forgot-password/verify-otp', {
          state: {
            email,
            token: result.data?.token,
            purpose: 'forgot_password'
          }
        });
      } else {
        setError(result.error || 'Failed to send reset email. Please try again.');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <Link to="/login" className="back-link">
            <ArrowLeft size={20} />
            Back to Login
          </Link>
          <h1>Reset Password</h1>
          <p>Enter your email address and we'll send you a verification code to reset your password.</p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="email">
              <Mail size={18} />
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              required
              disabled={isLoading}
              autoComplete="email"
            />
          </div>

          <div className="form-group">
            <ReCaptcha
              onChange={handleCaptchaChange}
              error={captchaError}
            />
          </div>

          {error && (
            <div className="error-message">
              <AlertCircle size={18} />
              {error}
            </div>
          )}

          <button
            type="submit"
            className="auth-button primary"
            disabled={isLoading || !email || !captchaToken}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" />
                Sending Reset Email...
              </>
            ) : (
              'Send Reset Email'
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Remember your password?{' '}
            <Link to="/login" className="auth-link">
              Sign In
            </Link>
          </p>
          <p>
            Don't have an account?{' '}
            <Link to="/signup" className="auth-link">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
