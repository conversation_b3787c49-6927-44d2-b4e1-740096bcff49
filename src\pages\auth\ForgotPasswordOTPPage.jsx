/**
 * Forgot Password OTP Verification Page - Reuses OTP verification logic for forgot password flow
 */
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { ArrowLeft, Shield, AlertCircle, RefreshCw } from 'lucide-react';
import { LoadingSpinner } from '../../components/ui';
import { useAuthService } from '../../hooks';
import '../../styles/AuthPages.css';

const ForgotPasswordOTPPage = () => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const authService = useAuthService();
  const inputRef = useRef(null);

  // Get email and token from navigation state
  const { email, token, purpose } = location.state || {};

  useEffect(() => {
    // Redirect if no email or token
    if (!email || !token) {
      navigate('/forgot-password');
      return;
    }

    // Focus on input
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Start countdown timer
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [email, token, navigate]);

  // Clear error when OTP changes
  useEffect(() => {
    if (error) setError('');
  }, [otp]);

  const handleOtpChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email || !token) {
      navigate('/forgot-password');
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP.');
      return;
    }

    setIsLoading(true);

    try {
      // Call the validateForgotPasswordOTP API
      const result = await authService.validateForgotPasswordOTP(email, otp, token);

      if (result.success) {
        // Navigate to reset password page with the new token
        navigate('/forgot-password/reset', {
          state: {
            email,
            verificationToken: result.data?.token || token,
            purpose: 'forgot_password'
          }
        });
      } else {
        setError(result.error || 'Invalid OTP. Please try again.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (!canResend || isResending || !email) return;

    setIsResending(true);
    setError('');

    try {
      // Call the resend forgot password verification API
      const result = await authService.resendForgotPasswordVerification(email);

      if (result.success) {
        // Reset timer and UI state
        setTimer(60);
        setCanResend(false);
        setOtp('');
        setError('');

        // Start countdown timer
        const interval = setInterval(() => {
          setTimer((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(interval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(result.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      setError('Failed to resend OTP. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  if (!email || !token) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <Link to="/forgot-password" className="back-link">
            <ArrowLeft size={20} />
            Back
          </Link>
          <h1>Verify Your Email</h1>
          <p>
            We've sent a 6-digit verification code to <strong>{email}</strong>.
            Please enter the code below to reset your password.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="otp">
              <Shield size={18} />
              Verification Code
            </label>
            <input
              ref={inputRef}
              type="text"
              id="otp"
              value={otp}
              onChange={handleOtpChange}
              placeholder="Enter 6-digit code"
              maxLength={6}
              required
              disabled={isLoading}
              autoComplete="one-time-code"
              className="otp-input"
            />
            <small className="form-hint">
              Enter the 6-digit code sent to your email
            </small>
          </div>

          {error && (
            <div className="error-message">
              <AlertCircle size={18} />
              {error}
            </div>
          )}

          <button
            type="submit"
            className="auth-button primary"
            disabled={isLoading || otp.length !== 6}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" />
                Verifying...
              </>
            ) : (
              'Verify Code'
            )}
          </button>

          <div className="resend-section">
            {canResend ? (
              <button
                type="button"
                onClick={handleResendOTP}
                className="resend-button"
                disabled={isResending}
              >
                {isResending ? (
                  <>
                    <LoadingSpinner size="small" />
                    Resending...
                  </>
                ) : (
                  <>
                    <RefreshCw size={16} />
                    Resend Code
                  </>
                )}
              </button>
            ) : (
              <p className="resend-timer">
                Resend code in {timer} seconds
              </p>
            )}
          </div>
        </form>

        <div className="auth-footer">
          <p>
            Remember your password?{' '}
            <Link to="/login" className="auth-link">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordOTPPage;
