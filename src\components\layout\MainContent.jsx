import React, { memo, useMemo, useCallback } from "react";
import { LoadingSpinner } from "../ui";
import { PDFHighlighter } from "../features";
import { withOptimization, useOptimizedComputed } from "../common/OptimizedComponent";
import "../../styles/MainContent.css";

const MainContent = memo(({ topic, textSize }) => {
  // Optimized PDF URL computation with caching
  const pdfUrl = useOptimizedComputed(() => {
    // Check direct pdfUrl property first
    if (topic?.pdfUrl) return topic.pdfUrl;

    // Check content body for PDF content
    const pdfContent = topic?.content?.body?.find(element => element.type === "pdf");
    return pdfContent?.pdfUrl;
  }, [topic?.pdfUrl, topic?.content?.body], { trackPerformance: true });

  // Memoized topic data to prevent unnecessary re-renders
  const topicData = useMemo(() => ({
    id: topic?.id,
    realTopicId: topic?.realTopicId || topic?.id,
    title: topic?.content?.heading || topic?.title,
    hasContent: Boolean(pdfUrl),
  }), [topic?.id, topic?.realTopicId, topic?.content?.heading, topic?.title, pdfUrl]);

  // Memoized loading component
  const LoadingComponent = useMemo(() => (
    <main className="main-content-panel">
      <LoadingSpinner size="large" message="Loading content..." />
    </main>
  ), []);

  // Memoized no content component
  const NoContentComponent = useMemo(() => (
    <div className="no-content">
      <p>No PDF available for this topic.</p>
    </div>
  ), []);

  // Early return for loading state
  if (!topic) {
    return LoadingComponent;
  }

  return (
    <main className={`main-content-panel text-size-${textSize}`}>
      <article className="lesson-content">
        <h1>{topicData.title}</h1>

        {/* Render PDF content */}
        <div className="lesson-content-body">
          {topicData.hasContent ? (
            <PDFHighlighter
              pdfUrl={pdfUrl}
              pageId={topicData.id}
              topicId={topicData.realTopicId}
              textSize={textSize}
            />
          ) : (
            NoContentComponent
          )}
        </div>
      </article>
    </main>
  );
});

// Display name for debugging
MainContent.displayName = "MainContent";

// Apply optimization wrapper
const OptimizedMainContent = withOptimization(MainContent, {
  displayName: 'MainContent',
  trackPerformance: true,
  shouldUpdate: (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.topic?.id !== nextProps.topic?.id ||
      prevProps.topic?.pdfUrl !== nextProps.topic?.pdfUrl ||
      prevProps.textSize !== nextProps.textSize ||
      prevProps.topic?.content?.heading !== nextProps.topic?.content?.heading
    );
  },
});

export default OptimizedMainContent;
