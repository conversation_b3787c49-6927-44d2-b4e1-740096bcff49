/**
 * Consolidated App Store - Combines frequently used stores for better performance
 * This reduces the number of store subscriptions and improves rendering performance
 */
import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Consolidated state interface
const initialState = {
  // UI State
  ui: {
    textSize: 'normal',
    theme: 'light',
    sidebarCollapsed: false,
    loading: false,
    errors: [],
    notifications: [],
  },
  
  // User preferences
  preferences: {
    autoSave: true,
    highlightMode: true,
    audioAutoplay: false,
    volume: 1,
    playbackRate: 1,
  },
  
  // Current session data
  session: {
    currentTopicId: null,
    currentPageId: null,
    lastActivity: null,
    viewHistory: [],
  },
  
  // Performance metrics
  performance: {
    renderCount: 0,
    lastRenderTime: null,
    slowOperations: [],
  }
};

export const useAppStore = create(
  subscribeWithSelector(
    persist(
      immer((set, get) => ({
        ...initialState,
        
        // UI Actions
        setTextSize: (size) => set((state) => {
          state.ui.textSize = size;
        }),
        
        setTheme: (theme) => set((state) => {
          state.ui.theme = theme;
        }),
        
        toggleSidebar: () => set((state) => {
          state.ui.sidebarCollapsed = !state.ui.sidebarCollapsed;
        }),
        
        setLoading: (loading) => set((state) => {
          state.ui.loading = loading;
        }),
        
        // Error management
        addError: (error) => set((state) => {
          const errorObj = {
            id: Date.now().toString(),
            message: typeof error === 'string' ? error : error.message,
            timestamp: new Date().toISOString(),
            type: 'error'
          };
          state.ui.errors.push(errorObj);
          
          // Auto-remove after 5 seconds
          setTimeout(() => {
            set((state) => {
              state.ui.errors = state.ui.errors.filter(e => e.id !== errorObj.id);
            });
          }, 5000);
        }),
        
        removeError: (errorId) => set((state) => {
          state.ui.errors = state.ui.errors.filter(e => e.id !== errorId);
        }),
        
        clearErrors: () => set((state) => {
          state.ui.errors = [];
        }),
        
        // Notification management
        addNotification: (notification) => set((state) => {
          const notificationObj = {
            id: Date.now().toString(),
            ...notification,
            timestamp: new Date().toISOString(),
          };
          state.ui.notifications.push(notificationObj);
          
          // Auto-remove after specified duration or 3 seconds
          const duration = notification.duration || 3000;
          setTimeout(() => {
            set((state) => {
              state.ui.notifications = state.ui.notifications.filter(n => n.id !== notificationObj.id);
            });
          }, duration);
        }),
        
        removeNotification: (notificationId) => set((state) => {
          state.ui.notifications = state.ui.notifications.filter(n => n.id !== notificationId);
        }),
        
        // Preferences
        updatePreferences: (updates) => set((state) => {
          Object.assign(state.preferences, updates);
        }),
        
        setHighlightMode: (enabled) => set((state) => {
          state.preferences.highlightMode = enabled;
        }),
        
        setVolume: (volume) => set((state) => {
          state.preferences.volume = Math.max(0, Math.min(1, volume));
        }),
        
        setPlaybackRate: (rate) => set((state) => {
          state.preferences.playbackRate = Math.max(0.5, Math.min(2, rate));
        }),
        
        // Session management
        setCurrentTopic: (topicId) => set((state) => {
          state.session.currentTopicId = topicId;
          state.session.lastActivity = Date.now();
          
          // Add to view history (keep last 10)
          const history = state.session.viewHistory.filter(id => id !== topicId);
          history.unshift(topicId);
          state.session.viewHistory = history.slice(0, 10);
        }),
        
        setCurrentPage: (pageId) => set((state) => {
          state.session.currentPageId = pageId;
          state.session.lastActivity = Date.now();
        }),
        
        updateActivity: () => set((state) => {
          state.session.lastActivity = Date.now();
        }),
        
        // Performance tracking
        incrementRenderCount: () => set((state) => {
          state.performance.renderCount += 1;
          state.performance.lastRenderTime = Date.now();
        }),
        
        recordSlowOperation: (operation, duration) => set((state) => {
          if (duration > 100) { // Only record operations slower than 100ms
            const record = {
              operation,
              duration,
              timestamp: Date.now(),
            };
            
            state.performance.slowOperations.push(record);
            
            // Keep only last 50 records
            if (state.performance.slowOperations.length > 50) {
              state.performance.slowOperations = state.performance.slowOperations.slice(-50);
            }
          }
        }),
        
        // Batch updates for performance
        batchUpdate: (updates) => set((state) => {
          if (typeof updates === 'function') {
            updates(state);
          } else {
            Object.keys(updates).forEach(key => {
              if (state[key] && typeof state[key] === 'object') {
                Object.assign(state[key], updates[key]);
              } else {
                state[key] = updates[key];
              }
            });
          }
        }),
        
        // Reset functions
        resetUI: () => set((state) => {
          Object.assign(state.ui, initialState.ui);
        }),
        
        resetSession: () => set((state) => {
          Object.assign(state.session, initialState.session);
        }),
        
        resetAll: () => set(() => ({ ...initialState })),
        
        // Selectors (computed values)
        getRecentTopics: () => {
          const state = get();
          return state.session.viewHistory.slice(0, 5);
        },
        
        getPerformanceMetrics: () => {
          const state = get();
          const slowOps = state.performance.slowOperations;
          return {
            totalRenders: state.performance.renderCount,
            lastRenderTime: state.performance.lastRenderTime,
            slowOperationsCount: slowOps.length,
            averageSlowOpDuration: slowOps.length > 0 
              ? slowOps.reduce((sum, op) => sum + op.duration, 0) / slowOps.length 
              : 0,
          };
        },
        
        hasErrors: () => {
          const state = get();
          return state.ui.errors.length > 0;
        },
        
        hasNotifications: () => {
          const state = get();
          return state.ui.notifications.length > 0;
        },
      })),
      {
        name: 'somayya-app-store',
        partialize: (state) => ({
          ui: {
            textSize: state.ui.textSize,
            theme: state.ui.theme,
            sidebarCollapsed: state.ui.sidebarCollapsed,
          },
          preferences: state.preferences,
          session: {
            viewHistory: state.session.viewHistory,
          },
        }),
      }
    )
  )
);

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  const incrementRenderCount = useAppStore(state => state.incrementRenderCount);
  const recordSlowOperation = useAppStore(state => state.recordSlowOperation);
  
  React.useEffect(() => {
    incrementRenderCount();
  });
  
  const measureOperation = React.useCallback(async (operationName, operation) => {
    const startTime = performance.now();
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      recordSlowOperation(operationName, duration);
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      recordSlowOperation(`${operationName} (error)`, duration);
      throw error;
    }
  }, [recordSlowOperation]);
  
  return { measureOperation };
};

// Optimized selectors to prevent unnecessary re-renders
export const useAppUI = () => useAppStore(state => state.ui);
export const useAppPreferences = () => useAppStore(state => state.preferences);
export const useAppSession = () => useAppStore(state => state.session);
export const useAppErrors = () => useAppStore(state => state.ui.errors);
export const useAppNotifications = () => useAppStore(state => state.ui.notifications);
