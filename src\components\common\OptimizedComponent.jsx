/**
 * Optimized Component Wrapper - Provides performance optimizations for React components
 * Includes memoization, error boundaries, and performance monitoring
 */
import React, { memo, useMemo, useCallback, useRef, useEffect } from 'react';
import { useAppStore } from '../../stores/appStore';

/**
 * Higher-order component for performance optimization
 * @param {React.Component} Component - Component to optimize
 * @param {Object} options - Optimization options
 * @returns {React.Component} Optimized component
 */
export const withOptimization = (Component, options = {}) => {
  const {
    displayName = Component.displayName || Component.name || 'Component',
    shouldUpdate = null,
    trackPerformance = false,
    errorBoundary = true,
  } = options;

  const OptimizedComponent = memo((props) => {
    const renderCountRef = useRef(0);
    const lastRenderTime = useRef(Date.now());
    const recordSlowOperation = useAppStore(state => state.recordSlowOperation);
    
    // Performance tracking
    useEffect(() => {
      if (trackPerformance) {
        renderCountRef.current += 1;
        const now = Date.now();
        const renderDuration = now - lastRenderTime.current;
        
        if (renderDuration > 16) { // Slower than 60fps
          recordSlowOperation(`${displayName} render`, renderDuration);
        }
        
        lastRenderTime.current = now;
      }
    });

    // Memoized props to prevent unnecessary re-renders
    const memoizedProps = useMemo(() => {
      const { children, ...otherProps } = props;
      return otherProps;
    }, [props]);

    // Error boundary wrapper
    if (errorBoundary) {
      return (
        <ErrorBoundaryWrapper componentName={displayName}>
          <Component {...memoizedProps}>
            {props.children}
          </Component>
        </ErrorBoundaryWrapper>
      );
    }

    return (
      <Component {...memoizedProps}>
        {props.children}
      </Component>
    );
  }, shouldUpdate);

  OptimizedComponent.displayName = `Optimized(${displayName})`;
  
  return OptimizedComponent;
};

/**
 * Error boundary wrapper component
 */
class ErrorBoundaryWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error(`Error in ${this.props.componentName}:`, error, errorInfo);
    
    // Report to error tracking service if available
    if (window.reportError) {
      window.reportError(error, {
        component: this.props.componentName,
        errorInfo,
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h3>Something went wrong in {this.props.componentName}</h3>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.message}</pre>
          </details>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for optimized event handlers
 * @param {Function} handler - Event handler function
 * @param {Array} deps - Dependencies array
 * @param {number} debounceMs - Debounce delay in milliseconds
 * @returns {Function} Optimized event handler
 */
export const useOptimizedHandler = (handler, deps = [], debounceMs = 0) => {
  const timeoutRef = useRef(null);
  
  return useCallback((...args) => {
    if (debounceMs > 0) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        handler(...args);
      }, debounceMs);
    } else {
      handler(...args);
    }
  }, [...deps, debounceMs]);
};

/**
 * Hook for optimized computed values
 * @param {Function} compute - Computation function
 * @param {Array} deps - Dependencies array
 * @param {Object} options - Options
 * @returns {*} Computed value
 */
export const useOptimizedComputed = (compute, deps = [], options = {}) => {
  const { 
    cacheSize = 10,
    trackPerformance = false 
  } = options;
  
  const cacheRef = useRef(new Map());
  const recordSlowOperation = useAppStore(state => state.recordSlowOperation);
  
  return useMemo(() => {
    const key = JSON.stringify(deps);
    
    // Check cache first
    if (cacheRef.current.has(key)) {
      return cacheRef.current.get(key);
    }
    
    // Compute new value
    const startTime = trackPerformance ? performance.now() : 0;
    const result = compute();
    
    if (trackPerformance) {
      const duration = performance.now() - startTime;
      if (duration > 10) {
        recordSlowOperation('computed value', duration);
      }
    }
    
    // Update cache
    if (cacheRef.current.size >= cacheSize) {
      const firstKey = cacheRef.current.keys().next().value;
      cacheRef.current.delete(firstKey);
    }
    cacheRef.current.set(key, result);
    
    return result;
  }, deps);
};

/**
 * Hook for lazy loading components
 * @param {Function} importFn - Dynamic import function
 * @param {Object} fallback - Fallback component while loading
 * @returns {Object} { Component, loading, error }
 */
export const useLazyComponent = (importFn, fallback = null) => {
  const [state, setState] = React.useState({
    Component: null,
    loading: true,
    error: null,
  });
  
  useEffect(() => {
    let cancelled = false;
    
    importFn()
      .then(module => {
        if (!cancelled) {
          setState({
            Component: module.default || module,
            loading: false,
            error: null,
          });
        }
      })
      .catch(error => {
        if (!cancelled) {
          setState({
            Component: null,
            loading: false,
            error,
          });
        }
      });
    
    return () => {
      cancelled = true;
    };
  }, [importFn]);
  
  if (state.loading) {
    return { Component: fallback, loading: true, error: null };
  }
  
  if (state.error) {
    return { 
      Component: () => (
        <div className="lazy-load-error">
          Failed to load component: {state.error.message}
        </div>
      ), 
      loading: false, 
      error: state.error 
    };
  }
  
  return state;
};

/**
 * Hook for intersection observer (lazy loading, infinite scroll)
 * @param {Object} options - Intersection observer options
 * @returns {Array} [ref, isIntersecting, entry]
 */
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [entry, setEntry] = React.useState(null);
  const elementRef = useRef(null);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      setEntry(entry);
    }, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options,
    });
    
    observer.observe(element);
    
    return () => {
      observer.unobserve(element);
    };
  }, [options.threshold, options.rootMargin]);
  
  return [elementRef, isIntersecting, entry];
};

/**
 * Performance monitoring component
 */
export const PerformanceMonitor = memo(({ children, componentName = 'Unknown' }) => {
  const renderCount = useRef(0);
  const recordSlowOperation = useAppStore(state => state.recordSlowOperation);
  
  useEffect(() => {
    renderCount.current += 1;
    const startTime = performance.now();
    
    return () => {
      const renderDuration = performance.now() - startTime;
      if (renderDuration > 16) {
        recordSlowOperation(`${componentName} lifecycle`, renderDuration);
      }
    };
  });
  
  return children;
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default {
  withOptimization,
  useOptimizedHandler,
  useOptimizedComputed,
  useLazyComponent,
  useIntersectionObserver,
  PerformanceMonitor,
};
