/**
 * Performance Monitoring Utilities
 * Comprehensive performance tracking and optimization tools
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
    this.thresholds = {
      renderTime: 16, // 60fps threshold
      apiCall: 1000, // 1 second
      bundleSize: 1024 * 1024, // 1MB
      memoryUsage: 50 * 1024 * 1024, // 50MB
    };
    
    this.init();
  }

  init() {
    // Initialize performance observers
    this.initRenderObserver();
    this.initResourceObserver();
    this.initMemoryObserver();
    this.initNavigationObserver();
  }

  /**
   * Initialize render performance observer
   */
  initRenderObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'measure') {
              this.recordMetric('render', {
                name: entry.name,
                duration: entry.duration,
                startTime: entry.startTime,
                timestamp: Date.now(),
              });
            }
          }
        });
        
        observer.observe({ entryTypes: ['measure'] });
        this.observers.set('render', observer);
      } catch (error) {
        console.warn('Failed to initialize render observer:', error);
      }
    }
  }

  /**
   * Initialize resource loading observer
   */
  initResourceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('resource', {
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize || 0,
              type: entry.initiatorType,
              timestamp: Date.now(),
            });
          }
        });
        
        observer.observe({ entryTypes: ['resource'] });
        this.observers.set('resource', observer);
      } catch (error) {
        console.warn('Failed to initialize resource observer:', error);
      }
    }
  }

  /**
   * Initialize memory observer
   */
  initMemoryObserver() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        this.recordMetric('memory', {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: Date.now(),
        });
      }, 10000); // Every 10 seconds
    }
  }

  /**
   * Initialize navigation observer
   */
  initNavigationObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('navigation', {
              type: entry.type,
              duration: entry.duration,
              domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
              loadComplete: entry.loadEventEnd - entry.loadEventStart,
              timestamp: Date.now(),
            });
          }
        });
        
        observer.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', observer);
      } catch (error) {
        console.warn('Failed to initialize navigation observer:', error);
      }
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(category, data) {
    if (!this.metrics.has(category)) {
      this.metrics.set(category, []);
    }
    
    const metrics = this.metrics.get(category);
    metrics.push(data);
    
    // Keep only last 100 entries per category
    if (metrics.length > 100) {
      metrics.splice(0, metrics.length - 100);
    }
    
    // Check thresholds and warn if exceeded
    this.checkThresholds(category, data);
  }

  /**
   * Check performance thresholds
   */
  checkThresholds(category, data) {
    switch (category) {
      case 'render':
        if (data.duration > this.thresholds.renderTime) {
          console.warn(`Slow render detected: ${data.name} took ${data.duration.toFixed(2)}ms`);
        }
        break;
      case 'resource':
        if (data.duration > this.thresholds.apiCall) {
          console.warn(`Slow resource load: ${data.name} took ${data.duration.toFixed(2)}ms`);
        }
        break;
      case 'memory':
        if (data.used > this.thresholds.memoryUsage) {
          console.warn(`High memory usage: ${(data.used / 1024 / 1024).toFixed(2)}MB`);
        }
        break;
    }
  }

  /**
   * Measure function execution time
   */
  measure(name, fn) {
    return async (...args) => {
      const startTime = performance.now();
      
      try {
        const result = await fn(...args);
        const endTime = performance.now();
        
        this.recordMetric('function', {
          name,
          duration: endTime - startTime,
          success: true,
          timestamp: Date.now(),
        });
        
        return result;
      } catch (error) {
        const endTime = performance.now();
        
        this.recordMetric('function', {
          name,
          duration: endTime - startTime,
          success: false,
          error: error.message,
          timestamp: Date.now(),
        });
        
        throw error;
      }
    };
  }

  /**
   * Mark start of a performance measurement
   */
  markStart(name) {
    performance.mark(`${name}-start`);
  }

  /**
   * Mark end of a performance measurement
   */
  markEnd(name) {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
  }

  /**
   * Get performance metrics
   */
  getMetrics(category = null) {
    if (category) {
      return this.metrics.get(category) || [];
    }
    
    const allMetrics = {};
    for (const [key, value] of this.metrics.entries()) {
      allMetrics[key] = value;
    }
    
    return allMetrics;
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const summary = {};
    
    for (const [category, metrics] of this.metrics.entries()) {
      if (metrics.length === 0) continue;
      
      const durations = metrics
        .filter(m => typeof m.duration === 'number')
        .map(m => m.duration);
      
      if (durations.length > 0) {
        summary[category] = {
          count: metrics.length,
          avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
          minDuration: Math.min(...durations),
          maxDuration: Math.max(...durations),
          p95Duration: this.percentile(durations, 0.95),
        };
      }
    }
    
    return summary;
  }

  /**
   * Calculate percentile
   */
  percentile(arr, p) {
    const sorted = arr.slice().sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * p) - 1;
    return sorted[index];
  }

  /**
   * Get Core Web Vitals
   */
  getCoreWebVitals() {
    return new Promise((resolve) => {
      const vitals = {};
      
      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            vitals.lcp = lastEntry.startTime;
            
            if (Object.keys(vitals).length === 3) {
              resolve(vitals);
            }
          }).observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
          console.warn('LCP measurement failed:', error);
        }
        
        // First Input Delay
        try {
          new PerformanceObserver((list) => {
            const firstInput = list.getEntries()[0];
            vitals.fid = firstInput.processingStart - firstInput.startTime;
            
            if (Object.keys(vitals).length === 3) {
              resolve(vitals);
            }
          }).observe({ entryTypes: ['first-input'] });
        } catch (error) {
          console.warn('FID measurement failed:', error);
        }
        
        // Cumulative Layout Shift
        try {
          let clsValue = 0;
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
            vitals.cls = clsValue;
            
            if (Object.keys(vitals).length === 3) {
              resolve(vitals);
            }
          }).observe({ entryTypes: ['layout-shift'] });
        } catch (error) {
          console.warn('CLS measurement failed:', error);
        }
      }
      
      // Fallback timeout
      setTimeout(() => resolve(vitals), 5000);
    });
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics.clear();
  }

  /**
   * Destroy performance monitor
   */
  destroy() {
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }
    this.observers.clear();
    this.metrics.clear();
  }
}

// Create global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = React.useState({});
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getSummary());
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  return {
    metrics,
    measure: performanceMonitor.measure.bind(performanceMonitor),
    markStart: performanceMonitor.markStart.bind(performanceMonitor),
    markEnd: performanceMonitor.markEnd.bind(performanceMonitor),
    getCoreWebVitals: performanceMonitor.getCoreWebVitals.bind(performanceMonitor),
  };
};

export default PerformanceMonitor;
