/**
 * Validation Utilities
 * Common validation functions for forms and user input
 */

/**
 * Validate email format
 * @param {string} email - Email address to validate
 * @returns {boolean} Whether email is valid
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with individual checks
 */
export const validatePassword = (password) => {
  const hasMinLength = password.length >= 8;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const isValid = hasMinLength && hasUppercase && hasLowercase && hasNumber && hasSpecial<PERSON>har;

  return {
    isValid,
    hasMin<PERSON>ength,
    hasUppercase,
    hasLowercase,
    hasNumber,
    hasSpecial<PERSON>har,
    score: [hasMinLength, hasUppercase, hasLowercase, hasNumber, hasSpecialChar].filter(Boolean).length
  };
};

/**
 * Validate password confirmation
 * @param {string} password - Original password
 * @param {string} confirmPassword - Password confirmation
 * @returns {boolean} Whether passwords match
 */
export const validatePasswordConfirmation = (password, confirmPassword) => {
  return password === confirmPassword && password.length > 0;
};

/**
 * Validate phone number (basic format)
 * @param {string} phone - Phone number to validate
 * @returns {boolean} Whether phone number is valid
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

/**
 * Validate required field
 * @param {string} value - Value to validate
 * @returns {boolean} Whether field has value
 */
export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0;
};

/**
 * Validate minimum length
 * @param {string} value - Value to validate
 * @param {number} minLength - Minimum required length
 * @returns {boolean} Whether value meets minimum length
 */
export const validateMinLength = (value, minLength) => {
  return value && value.length >= minLength;
};

/**
 * Validate maximum length
 * @param {string} value - Value to validate
 * @param {number} maxLength - Maximum allowed length
 * @returns {boolean} Whether value is within maximum length
 */
export const validateMaxLength = (value, maxLength) => {
  return !value || value.length <= maxLength;
};

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} Whether URL is valid
 */
export const validateUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate numeric value
 * @param {string|number} value - Value to validate
 * @returns {boolean} Whether value is numeric
 */
export const validateNumeric = (value) => {
  return !isNaN(value) && !isNaN(parseFloat(value));
};

/**
 * Validate integer value
 * @param {string|number} value - Value to validate
 * @returns {boolean} Whether value is an integer
 */
export const validateInteger = (value) => {
  return Number.isInteger(Number(value));
};

/**
 * Validate value is within range
 * @param {number} value - Value to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {boolean} Whether value is within range
 */
export const validateRange = (value, min, max) => {
  const num = Number(value);
  return !isNaN(num) && num >= min && num <= max;
};

/**
 * Validate date format (YYYY-MM-DD)
 * @param {string} date - Date string to validate
 * @returns {boolean} Whether date is valid
 */
export const validateDate = (date) => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) return false;
  
  const dateObj = new Date(date);
  return dateObj instanceof Date && !isNaN(dateObj);
};

/**
 * Validate age (must be 18 or older)
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {boolean} Whether person is 18 or older
 */
export const validateAge = (birthDate) => {
  if (!validateDate(birthDate)) return false;
  
  const today = new Date();
  const birth = new Date(birthDate);
  const age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    return age - 1 >= 18;
  }
  
  return age >= 18;
};

/**
 * Validate file size
 * @param {File} file - File to validate
 * @param {number} maxSizeInMB - Maximum size in megabytes
 * @returns {boolean} Whether file size is valid
 */
export const validateFileSize = (file, maxSizeInMB) => {
  if (!file) return false;
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return file.size <= maxSizeInBytes;
};

/**
 * Validate file type
 * @param {File} file - File to validate
 * @param {string[]} allowedTypes - Array of allowed MIME types
 * @returns {boolean} Whether file type is allowed
 */
export const validateFileType = (file, allowedTypes) => {
  if (!file) return false;
  return allowedTypes.includes(file.type);
};

/**
 * Validate form data against rules
 * @param {Object} data - Form data to validate
 * @param {Object} rules - Validation rules
 * @returns {Object} Validation result with errors
 */
export const validateForm = (data, rules) => {
  const errors = {};
  let isValid = true;

  Object.keys(rules).forEach(field => {
    const value = data[field];
    const fieldRules = rules[field];
    const fieldErrors = [];

    if (fieldRules.required && !validateRequired(value)) {
      fieldErrors.push('This field is required');
    }

    if (value && fieldRules.email && !validateEmail(value)) {
      fieldErrors.push('Please enter a valid email address');
    }

    if (value && fieldRules.minLength && !validateMinLength(value, fieldRules.minLength)) {
      fieldErrors.push(`Minimum length is ${fieldRules.minLength} characters`);
    }

    if (value && fieldRules.maxLength && !validateMaxLength(value, fieldRules.maxLength)) {
      fieldErrors.push(`Maximum length is ${fieldRules.maxLength} characters`);
    }

    if (value && fieldRules.numeric && !validateNumeric(value)) {
      fieldErrors.push('Please enter a valid number');
    }

    if (value && fieldRules.url && !validateUrl(value)) {
      fieldErrors.push('Please enter a valid URL');
    }

    if (fieldRules.custom && typeof fieldRules.custom === 'function') {
      const customResult = fieldRules.custom(value, data);
      if (customResult !== true) {
        fieldErrors.push(customResult);
      }
    }

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors;
      isValid = false;
    }
  });

  return {
    isValid,
    errors
  };
};

/**
 * Get password strength text
 * @param {number} score - Password strength score (0-5)
 * @returns {string} Strength description
 */
export const getPasswordStrengthText = (score) => {
  switch (score) {
    case 0:
    case 1:
      return 'Very Weak';
    case 2:
      return 'Weak';
    case 3:
      return 'Fair';
    case 4:
      return 'Good';
    case 5:
      return 'Strong';
    default:
      return 'Unknown';
  }
};

/**
 * Get password strength color
 * @param {number} score - Password strength score (0-5)
 * @returns {string} Color class or hex code
 */
export const getPasswordStrengthColor = (score) => {
  switch (score) {
    case 0:
    case 1:
      return '#ff4444';
    case 2:
      return '#ff8800';
    case 3:
      return '#ffaa00';
    case 4:
      return '#88cc00';
    case 5:
      return '#00cc44';
    default:
      return '#cccccc';
  }
};
