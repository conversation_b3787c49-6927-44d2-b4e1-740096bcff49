/**
 * Reset Password Page - Reuses CreatePasswordPage structure for forgot password flow
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { ArrowLeft, Lock, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { LoadingSpinner } from '../../components/ui';
import { useAuthService } from '../../hooks';
import { validatePassword } from '../../utils/validation';
import '../../styles/AuthPages.css';

const ResetPasswordPage = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const navigate = useNavigate();
  const location = useLocation();
  const authService = useAuthService();

  // Get email and verification token from navigation state
  const { email, verificationToken, purpose } = location.state || {};

  // Password validation
  const passwordValidation = validatePassword(password);

  useEffect(() => {
    // Redirect if no email or verification token
    if (!email || !verificationToken) {
      navigate('/forgot-password');
      return;
    }
  }, [email, verificationToken, navigate]);

  // Clear error when inputs change
  useEffect(() => {
    if (error) setError('');
  }, [password, confirmPassword]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email || !verificationToken) {
      navigate('/forgot-password');
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Password does not meet the requirements.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    setIsLoading(true);

    try {
      // Call the resetPassword API
      const result = await authService.resetPassword(
        email,
        password,
        confirmPassword,
        verificationToken
      );

      if (result.success) {
        // Navigate to login with success message
        navigate('/login', {
          state: {
            message: 'Password reset successfully! Please sign in with your new password.',
            type: 'success'
          }
        });
      } else {
        setError(result.error || 'Failed to reset password. Please try again.');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!email || !verificationToken) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <Link to="/forgot-password/verify-otp" className="back-link">
            <ArrowLeft size={20} />
            Back
          </Link>
          <h1>Reset Your Password</h1>
          <p>Create a new password for your account: <strong>{email}</strong></p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="form-group">
            <label htmlFor="password">
              <Lock size={18} />
              New Password
            </label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your new password"
                required
                disabled={isLoading}
                autoComplete="new-password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword">
              <Lock size={18} />
              Confirm New Password
            </label>
            <div className="password-input-container">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
                required
                disabled={isLoading}
                autoComplete="new-password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          {/* Password Requirements */}
          <div className="password-requirements">
            <h4>Password Requirements:</h4>
            <ul>
              <li className={passwordValidation.hasMinLength ? 'valid' : 'invalid'}>
                {passwordValidation.hasMinLength ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                At least 8 characters
              </li>
              <li className={passwordValidation.hasUppercase ? 'valid' : 'invalid'}>
                {passwordValidation.hasUppercase ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                One uppercase letter
              </li>
              <li className={passwordValidation.hasLowercase ? 'valid' : 'invalid'}>
                {passwordValidation.hasLowercase ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                One lowercase letter
              </li>
              <li className={passwordValidation.hasNumber ? 'valid' : 'invalid'}>
                {passwordValidation.hasNumber ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                One number
              </li>
              <li className={passwordValidation.hasSpecialChar ? 'valid' : 'invalid'}>
                {passwordValidation.hasSpecialChar ? <CheckCircle size={16} /> : <AlertCircle size={16} />}
                One special character
              </li>
            </ul>
          </div>

          {error && (
            <div className="error-message">
              <AlertCircle size={18} />
              {error}
            </div>
          )}

          <button
            type="submit"
            className="auth-button primary"
            disabled={isLoading || !passwordValidation.isValid || password !== confirmPassword}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" />
                Resetting Password...
              </>
            ) : (
              'Reset Password'
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Remember your password?{' '}
            <Link to="/login" className="auth-link">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
