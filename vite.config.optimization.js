/**
 * Vite Configuration Optimizations
 * Advanced build optimizations for better performance and smaller bundle sizes
 */
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react({
      // Enable React Fast Refresh
      fastRefresh: true,
      // Optimize JSX runtime
      jsxRuntime: 'automatic',
      // Enable babel plugins for optimization
      babel: {
        plugins: [
          // Remove console.log in production
          process.env.NODE_ENV === 'production' && [
            'transform-remove-console',
            { exclude: ['error', 'warn'] }
          ],
          // Optimize React components
          process.env.NODE_ENV === 'production' && [
            'babel-plugin-transform-react-remove-prop-types'
          ],
        ].filter(Boolean),
      },
    }),
  ],

  // Build optimizations
  build: {
    // Target modern browsers for smaller bundles
    target: 'es2020',
    
    // Enable minification
    minify: 'terser',
    
    // Terser options for better compression
    terserOptions: {
      compress: {
        // Remove console.log in production
        drop_console: true,
        drop_debugger: true,
        // Remove unused code
        dead_code: true,
        // Optimize conditionals
        conditionals: true,
        // Optimize comparisons
        comparisons: true,
        // Optimize sequences
        sequences: true,
        // Optimize properties
        properties: true,
        // Optimize unused variables
        unused: true,
      },
      mangle: {
        // Mangle property names for smaller size
        properties: {
          regex: /^_/,
        },
      },
      format: {
        // Remove comments
        comments: false,
      },
    },

    // Rollup options for advanced bundling
    rollupOptions: {
      // Code splitting configuration
      output: {
        // Manual chunks for better caching
        manualChunks: {
          // Vendor chunk for third-party libraries
          vendor: [
            'react',
            'react-dom',
            'react-router-dom',
          ],
          
          // UI components chunk
          ui: [
            'lucide-react',
          ],
          
          // PDF handling chunk
          pdf: [
            'react-pdf',
            'pdfjs-dist',
            'react-pdf-highlighter',
          ],
          
          // State management chunk
          state: [
            'zustand',
          ],
          
          // Rich text editor chunk
          editor: [
            'slate',
            'slate-react',
            'slate-history',
          ],
          
          // Firebase chunk
          firebase: [
            'firebase/app',
            'firebase/auth',
          ],
          
          // HTTP client chunk
          http: [
            'axios',
          ],
        },
        
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop().replace('.js', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        
        // Optimize asset file names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
      },
      
      // External dependencies (if using CDN)
      external: process.env.USE_CDN ? [
        // 'react',
        // 'react-dom',
      ] : [],
    },

    // Source map configuration
    sourcemap: process.env.NODE_ENV === 'development',
    
    // Chunk size warning limit
    chunkSizeWarningLimit: 1000,
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Report compressed size
    reportCompressedSize: true,
  },

  // Development server optimizations
  server: {
    // Enable HTTP/2
    http2: false, // Set to true if you have HTTPS
    
    // Optimize HMR
    hmr: {
      overlay: true,
    },
    
    // Pre-transform known dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'zustand',
        'axios',
        'lucide-react',
      ],
      exclude: [
        // Exclude large dependencies that don't need pre-bundling
        'pdfjs-dist',
      ],
    },
  },

  // Resolve optimizations
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@config': resolve(__dirname, 'src/config'),
    },
  },

  // CSS optimizations
  css: {
    // CSS modules configuration
    modules: {
      localsConvention: 'camelCase',
      generateScopedName: process.env.NODE_ENV === 'production' 
        ? '[hash:base64:5]' 
        : '[name]__[local]__[hash:base64:5]',
    },
    
    // PostCSS configuration
    postcss: {
      plugins: [
        // Autoprefixer for browser compatibility
        require('autoprefixer'),
        
        // CSS optimization in production
        ...(process.env.NODE_ENV === 'production' ? [
          require('cssnano')({
            preset: ['default', {
              discardComments: { removeAll: true },
              normalizeWhitespace: true,
              colormin: true,
              convertValues: true,
              discardDuplicates: true,
              discardEmpty: true,
              mergeRules: true,
              minifyFontValues: true,
              minifyParams: true,
              minifySelectors: true,
              reduceIdents: true,
              reduceTransforms: true,
              uniqueSelectors: true,
            }],
          }),
        ] : []),
      ],
    },
  },

  // Performance optimizations
  optimizeDeps: {
    // Force optimization of these packages
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'zustand',
      'axios',
    ],
    
    // Exclude packages that should not be optimized
    exclude: [
      'pdfjs-dist',
      'react-pdf-highlighter',
    ],
    
    // ESBuild options for dependency optimization
    esbuildOptions: {
      target: 'es2020',
      supported: {
        'top-level-await': true,
      },
    },
  },

  // ESBuild configuration for faster builds
  esbuild: {
    // Target modern browsers
    target: 'es2020',
    
    // Drop console and debugger in production
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    
    // Optimize for size in production
    minifyIdentifiers: process.env.NODE_ENV === 'production',
    minifySyntax: process.env.NODE_ENV === 'production',
    minifyWhitespace: process.env.NODE_ENV === 'production',
  },

  // Define global constants
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
  },

  // Worker configuration
  worker: {
    format: 'es',
    plugins: [react()],
  },
});
