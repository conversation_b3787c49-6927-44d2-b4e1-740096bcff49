/**
 * Advanced API Cache Manager - Intelligent caching with TTL, invalidation, and persistence
 */

class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.ttlMap = new Map();
    this.accessMap = new Map(); // For LRU tracking
    this.maxSize = options.maxSize || 1000;
    this.defaultTTL = options.defaultTTL || 300000; // 5 minutes
    this.persistKey = options.persistKey || 'api-cache';
    this.enablePersistence = options.enablePersistence !== false;
    this.cleanupInterval = options.cleanupInterval || 60000; // 1 minute
    
    // Load persisted cache
    if (this.enablePersistence) {
      this.loadFromStorage();
    }
    
    // Start cleanup interval
    this.startCleanup();
  }

  /**
   * Generate cache key from request config
   */
  generateKey(config) {
    const { method = 'GET', url, params, data } = config;
    const key = `${method.toUpperCase()}:${url}`;
    
    if (params && Object.keys(params).length > 0) {
      const sortedParams = Object.keys(params)
        .sort()
        .map(k => `${k}=${params[k]}`)
        .join('&');
      return `${key}?${sortedParams}`;
    }
    
    if (data && method !== 'GET') {
      const dataHash = this.hashObject(data);
      return `${key}:${dataHash}`;
    }
    
    return key;
  }

  /**
   * Simple hash function for objects
   */
  hashObject(obj) {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Set cache entry with TTL
   */
  set(key, value, ttl = this.defaultTTL) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    const now = Date.now();
    const expiresAt = now + ttl;
    
    this.cache.set(key, {
      value,
      createdAt: now,
      expiresAt,
      accessCount: 1,
      lastAccessed: now,
    });
    
    this.ttlMap.set(key, expiresAt);
    this.accessMap.set(key, now);
    
    // Persist to storage
    if (this.enablePersistence) {
      this.persistToStorage();
    }
  }

  /**
   * Get cache entry
   */
  get(key) {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    const now = Date.now();
    
    // Check if expired
    if (now > entry.expiresAt) {
      this.delete(key);
      return null;
    }
    
    // Update access tracking
    entry.accessCount += 1;
    entry.lastAccessed = now;
    this.accessMap.set(key, now);
    
    return entry.value;
  }

  /**
   * Check if key exists and is not expired
   */
  has(key) {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete cache entry
   */
  delete(key) {
    this.cache.delete(key);
    this.ttlMap.delete(key);
    this.accessMap.delete(key);
    
    if (this.enablePersistence) {
      this.persistToStorage();
    }
  }

  /**
   * Clear all cache entries
   */
  clear() {
    this.cache.clear();
    this.ttlMap.clear();
    this.accessMap.clear();
    
    if (this.enablePersistence) {
      localStorage.removeItem(this.persistKey);
    }
  }

  /**
   * Evict least recently used entries
   */
  evictLRU() {
    const entries = Array.from(this.accessMap.entries())
      .sort((a, b) => a[1] - b[1]); // Sort by access time
    
    const toEvict = Math.ceil(this.maxSize * 0.1); // Evict 10%
    
    for (let i = 0; i < toEvict && i < entries.length; i++) {
      const [key] = entries[i];
      this.delete(key);
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, expiresAt] of this.ttlMap.entries()) {
      if (now > expiresAt) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.delete(key));
    
    return expiredKeys.length;
  }

  /**
   * Start automatic cleanup
   */
  startCleanup() {
    this.cleanupTimer = setInterval(() => {
      const cleaned = this.cleanup();
      if (cleaned > 0) {
        console.log(`Cache cleanup: removed ${cleaned} expired entries`);
      }
    }, this.cleanupInterval);
  }

  /**
   * Stop automatic cleanup
   */
  stopCleanup() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let expired = 0;
    let totalSize = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expired += 1;
      }
      totalSize += JSON.stringify(entry.value).length;
    }
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      expired,
      totalSizeBytes: totalSize,
      hitRate: this.calculateHitRate(),
    };
  }

  /**
   * Calculate cache hit rate
   */
  calculateHitRate() {
    let totalAccess = 0;
    for (const entry of this.cache.values()) {
      totalAccess += entry.accessCount;
    }
    return totalAccess > 0 ? (this.cache.size / totalAccess) : 0;
  }

  /**
   * Persist cache to localStorage
   */
  persistToStorage() {
    if (!this.enablePersistence) return;
    
    try {
      const cacheData = {
        cache: Array.from(this.cache.entries()),
        ttlMap: Array.from(this.ttlMap.entries()),
        accessMap: Array.from(this.accessMap.entries()),
        timestamp: Date.now(),
      };
      
      localStorage.setItem(this.persistKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to persist cache to storage:', error);
    }
  }

  /**
   * Load cache from localStorage
   */
  loadFromStorage() {
    if (!this.enablePersistence) return;
    
    try {
      const stored = localStorage.getItem(this.persistKey);
      if (!stored) return;
      
      const cacheData = JSON.parse(stored);
      const now = Date.now();
      
      // Only load if not too old (1 hour)
      if (now - cacheData.timestamp > 3600000) {
        localStorage.removeItem(this.persistKey);
        return;
      }
      
      // Restore cache entries
      for (const [key, entry] of cacheData.cache) {
        if (now < entry.expiresAt) {
          this.cache.set(key, entry);
        }
      }
      
      // Restore TTL map
      for (const [key, expiresAt] of cacheData.ttlMap) {
        if (now < expiresAt) {
          this.ttlMap.set(key, expiresAt);
        }
      }
      
      // Restore access map
      for (const [key, accessTime] of cacheData.accessMap) {
        if (this.cache.has(key)) {
          this.accessMap.set(key, accessTime);
        }
      }
      
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
      localStorage.removeItem(this.persistKey);
    }
  }

  /**
   * Destroy cache manager
   */
  destroy() {
    this.stopCleanup();
    this.clear();
  }
}

// Create default cache manager instance
export const defaultCacheManager = new CacheManager({
  maxSize: 500,
  defaultTTL: 300000, // 5 minutes
  enablePersistence: true,
  persistKey: 'somayya-api-cache',
});

// Cache strategies
export const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  CACHE_ONLY: 'cache-only',
  NETWORK_ONLY: 'network-only',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
};

export default CacheManager;
